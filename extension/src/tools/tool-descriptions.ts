import { ToolDescription } from '@the-agent/shared';

export const TAB_TOOLKIT_TOOLS: ToolDescription[] = [
  {
    name: 'TabToolkit_openTab',
    description:
      'Open a new browser tab with the specified URL. If a tab with the same URL already exists, switches to the existing tab instead of creating a duplicate.',
    parameters: {
      type: 'object',
      properties: {
        url: {
          type: 'string',
          description: 'The URL to open',
        },
      },
      required: ['url'],
    },
    scope: {
      urlPatterns: ['<all_urls>'],
      agents: ['browser', 'workflow'],
    },
  },
  {
    name: 'TabToolkit_closeTab',
    description: 'Close a specific tab by its ID',
    parameters: {
      type: 'object',
      properties: {
        tabId: {
          type: 'number',
          description: 'The ID of the tab to close',
        },
      },
      required: ['tabId'],
    },
    scope: {
      urlPatterns: ['<all_urls>'],
      agents: ['browser', 'workflow'],
    },
  },
  {
    name: 'TabToolkit_switchToTab',
    description: 'Switch to a specific tab by its ID',
    parameters: {
      type: 'object',
      properties: {
        tabId: {
          type: 'number',
          description: 'The ID of the tab to switch to',
        },
      },
      required: ['tabId'],
    },
    scope: {
      urlPatterns: ['<all_urls>'],
      agents: ['browser', 'workflow'],
    },
  },
];

export const WEB_TOOLKIT_ACTION_TOOLS: ToolDescription[] = [
  {
    name: 'WebToolkit_input',
    description:
      'Types text into an input element using its selector or index. Supports clearing existing content and optionally pressing Enter after input. For best reliability, use element index numbers from WebToolkit_analyzePageDOM instead of label-based selectors which may be unstable.',
    parameters: {
      type: 'object',
      properties: {
        selectorOrIndex: {
          type: 'string',
          description:
            'Element identifier - can be either an index number from page analysis or a CSS selector. The system will try multiple selector strategies if the primary one fails.',
        },
        value: {
          type: 'string',
          description: 'The text to input into the element',
        },
        clearFirst: {
          type: 'boolean',
          description:
            'If true, selects and replaces existing content. If false, appends to existing content. Default: false',
        },
        pressEnterAfterInput: {
          type: 'boolean',
          description:
            'If true, simulates pressing the Enter key after inputting the value. Useful for submitting search forms or triggering actions. Default: false',
        },
      },
      required: ['selectorOrIndex', 'value'],
    },
    scope: {
      urlPatterns: ['<all_urls>'],
      agents: ['browser'],
    },
  },
  {
    name: 'WebToolkit_scroll',
    description:
      'Scroll the page to the bottom of the page. If selectorOrIndex is provided, scroll to bring an element into view using its selector or index. For best reliability, use element index numbers from WebToolkit_analyzePageDOM instead of label-based selectors which may be unstable.',
    parameters: {
      type: 'object',
      properties: {
        selectorOrIndex: {
          type: 'string',
          description:
            'Element identifier - can be either an index number from page analysis or a CSS selector. The system will try multiple selector strategies if the primary one fails.',
        },
      },
    },
    scope: {
      urlPatterns: ['<all_urls>'],
      agents: ['browser'],
    },
  },
  {
    name: 'WebToolkit_refreshPage',
    description:
      "Refresh the current page based on the user's context. This will reload the current page and wait for it to be fully loaded. The page to refresh is determined by the user's current context and cannot be specified directly.",
    parameters: {
      type: 'object',
      properties: {
        timeout: {
          type: 'number',
          description: 'Maximum time to wait for page load in milliseconds. Default: 5000',
        },
      },
    },
    scope: {
      urlPatterns: ['<all_urls>'],
      agents: ['browser'],
    },
  },
  {
    name: 'WebToolkit_click',
    description:
      'Click an element using its selector or index. For best reliability, use element index numbers from WebToolkit_analyzePageDOM instead of label-based selectors which may be unstable.',
    parameters: {
      type: 'object',
      properties: {
        selectorOrIndex: {
          type: 'string',
          description:
            'Element identifier - can be either an index number from page analysis or a CSS selector. The system will try multiple selector strategies if the primary one fails.',
        },
      },
      required: ['selectorOrIndex'],
    },
    scope: {
      urlPatterns: ['<all_urls>'],
      agents: ['browser'],
    },
  },
  {
    name: 'WebToolkit_sendKeys',
    description:
      'Send strings of special keys like Escape, Backspace, Insert, PageDown, Delete, Enter. Shortcuts such as Control+o, Control+Shift+T are supported as well.',
    parameters: {
      type: 'object',
      properties: {
        keys: {
          type: 'string',
          description:
            'Keys to send. Can be special keys (Escape, Enter, Backspace, Delete, Insert, PageUp, PageDown, Home, End, Tab, Space, ArrowLeft, ArrowUp, ArrowRight, ArrowDown, F1-F12) or key combinations (Control+c, Control+Shift+T, Alt+Tab). Multiple keys can be separated by spaces.',
        },
      },
      required: ['keys'],
    },
    scope: {
      urlPatterns: ['<all_urls>'],
      agents: ['browser'],
    },
  },
];

export const WEB_TOOLKIT_ANALYZE_TOOLS: ToolDescription[] = [
  {
    name: 'WebToolkit_analyzePageDOM',
    description: `
Analyze the DOM structure when you need to understand the page layout or find specific elements. Use this sparingly - only when you're unsure about the page structure or need to locate elements that aren't obvious.

Each DOM element will be printed as one line of text while the indentation level indicates the depth of the element in the DOM tree.

**Enhanced Selector Strategy**: Each element now provides multiple selector options for better stability:
- **Primary selector**: The most reliable selector (ID, data-testid, or unique attribute)
- **Alternative selectors**: Backup options including role-based selectors, aria attributes, and tag-based selectors
- **Stable attributes focus**: Uses only stable attributes like ID, data-testid, name, aria-label, role, title, placeholder, type
- **Index**: Still available for immediate use, but may change if page content updates

Example output:
- [0] button [label="Login", selector="#login-btn", alt="button.login-btn | [role=button]"]
- [1] input [label="Username", type="text", selector="[name=username]", alt="input[type=text] | button"]

**Element Selection Priority**:
1. **Index** (e.g., "0", "1") - Fastest but may change with page updates
2. **Primary selector** - Most stable, use when available
3. **Alternative selectors** - Fallback options if primary fails
`,
    parameters: {
      type: 'object',
      properties: {
        selector: {
          type: 'string',
          description:
            'Optional selector to focus the analysis on a specific element and its descendants. Use this to narrow the scope and improve performance. If not provided, analyzes all page elements.',
        },
      },
    },
    returns: {
      type: 'object',
      description: 'Element map of the current page',
      properties: {
        success: {
          type: 'boolean',
          description: 'Whether the element map was successfully built',
        },
        error: {
          type: 'string',
          description: 'Error message if element map building failed',
        },
        data: {
          type: 'object',
          description: 'Element map of the current page',
          properties: {
            domTree: {
              type: 'string',
              description: 'DOM tree of the current page',
            },
          },
        },
      },
    },
    scope: {
      urlPatterns: ['<all_urls>'],
      agents: ['browser'],
    },
  },
  {
    name: 'WebToolkit_extractText',
    description: 'Extract text from the current page',
    parameters: {
      type: 'object',
      properties: {},
    },
    returns: {
      type: 'object',
      description: 'Markdown content from the page',
      properties: {
        success: {
          type: 'boolean',
          description: 'Whether the operation was successful',
        },
        data: {
          type: 'object',
          description: 'Page content data',
          properties: {
            content: {
              type: 'string',
              description: 'The page content converted to Markdown format',
            },
            url: {
              type: 'string',
              description: 'The URL of the page',
            },
            title: {
              type: 'string',
              description: 'The title of the page',
            },
          },
        },
        error: {
          type: 'string',
          description: 'Error message if the operation failed',
        },
      },
    },
    scope: {
      urlPatterns: ['<all_urls>'],
      agents: ['browser', 'dev', 'workflow'],
    },
  },
  {
    name: 'WebToolkit_screenshot',
    description: 'Take a screenshot of the current page',
    parameters: {
      type: 'object',
      properties: {
        fullPage: {
          type: 'boolean',
          description: 'Whether to capture the full page or just the viewport',
        },
      },
    },
    returns: {
      type: 'object',
      description: 'Screenshot data',
      properties: {
        dataUrl: {
          type: 'string',
          description:
            'Base64 encoded data of the screenshot or image. If the screenshot is not available, the dataUrl will be an empty string.',
        },
        success: {
          type: 'boolean',
          description: 'Whether the screenshot was successfully taken',
        },
      },
    },
    scope: {
      urlPatterns: ['<all_urls>'],
      agents: ['browser', 'dev', 'workflow'],
    },
  },
  {
    name: 'WebToolkit_getSimplifiedPageDOM',
    description: 'Get a simplified DOM structure with the overall structure of the web page.',
    parameters: {
      type: 'object',
      properties: {},
    },
    returns: {
      type: 'object',
      description: 'Simplified DOM structure of the current page',
      properties: {
        success: {
          type: 'boolean',
          description: 'Whether the operation was successful',
        },
        error: {
          type: 'string',
          description: 'Error message if the operation failed',
        },
        data: {
          type: 'object',
          description: 'Simplified DOM structure of the current page',
          properties: {
            domTree: {
              type: 'string',
              description: 'Simplified DOM structure of the current page',
            },
          },
        },
      },
    },
    scope: {
      urlPatterns: ['<all_urls>'],
      agents: ['dev', 'workflow'],
    },
  },
  {
    name: 'WebToolkit_getDetailedHtmlElement',
    description: 'Get a detailed HTML element with all attributes and content.',
    parameters: {
      type: 'object',
      properties: {
        selector: {
          type: 'string',
          description: 'Selector of the element to get the details of',
        },
      },
      required: ['selector'],
    },
    returns: {
      type: 'object',
      description: 'Detailed HTML element',
      properties: {
        success: {
          type: 'boolean',
          description: 'Whether the operation was successful',
        },
        error: {
          type: 'string',
          description: 'Error message if the operation failed',
        },
        data: {
          type: 'object',
          description: 'Detailed HTML element',
          properties: {
            html: {
              type: 'string',
              description: 'HTML content of the element',
            },
          },
        },
      },
    },
    scope: {
      urlPatterns: ['<all_urls>'],
      agents: ['dev', 'workflow'],
    },
  },
];

export const DEV_TOOLKIT_TOOLS: ToolDescription[] = [
  {
    name: 'DevToolkit_deliver',
    description: "Deliver the script generated per user's request",
    parameters: {
      type: 'object',
      properties: {
        script: {
          type: 'string',
          description: "The script generated per user's request",
        },
      },
    },
    returns: {
      type: 'null',
      description: 'No return value',
    },
    scope: {
      urlPatterns: ['<all_urls>'],
      agents: ['dev'],
    },
  },
];

export const WORKFLOW_TOOLKIT_TOOLS: ToolDescription[] = [
  {
    name: 'WorkflowToolkit_deliver',
    description: "Deliver the workflow generated per user's request.",
    parameters: {
      type: 'object',
      properties: {
        workflow: {
          type: 'array',
          description: "The workflow generated per user's request as a node list",
          items: {
            type: 'object',
            description: 'A workflow node definition',
          },
        },
        parameters: {
          type: 'array',
          description: 'Parameters definition for the workflow. The parameter name must be unique.',
          items: {
            type: 'object',
            description: 'One parameter definition',
            properties: {
              name: {
                type: 'string',
                description: 'The name of the parameter',
              },
              type: {
                type: 'string',
                description: 'The type of the parameter',
              },
              description: {
                type: 'string',
                description: 'The description of the parameter',
              },
              optional: {
                type: 'boolean',
                description: 'Whether the parameter is optional, default is false',
              },
              default: {
                type: 'string',
                description: 'The default value of the parameter',
              },
              sensitive: {
                type: 'boolean',
                description: 'Whether the parameter is sensitive, default is false',
              },
            },
          },
        },
      },
      required: ['workflow', 'parameters'],
    },
    returns: {
      type: 'null',
      description: 'No return value',
    },
    scope: {
      urlPatterns: ['<all_urls>'],
      agents: ['workflow'],
    },
  },
  {
    name: 'WorkflowToolkit_run',
    description:
      "Run the current workflow generated per user's request. The workflow must be delivered first.",
    parameters: {
      type: 'object',
      properties: {
        parameters: {
          type: 'object',
          description:
            'Parameters for the workflow as key value. The key is the parameter name and the value is the parameter value, which can be a string, number, boolean, array, or object.',
        },
      },
    },
    returns: {
      type: 'null',
      description: 'No return value',
    },
    scope: {
      urlPatterns: ['<all_urls>'],
      agents: ['workflow'],
    },
  },
  {
    name: 'WorkflowToolkit_runNode',
    description:
      'Run a arbitrary node. This is mainly for debugging and testing purposes. LLM can create a node on the fly to run to test some arbitrary logic.',
    parameters: {
      type: 'object',
      properties: {
        node: {
          type: 'object',
          description: 'The node to run. The node must be a valid node definition.',
        },
        parameters: {
          type: 'object',
          description:
            'Parameters for the workflow as key value. The key is the parameter name and the value is the parameter value, which can be a string, number, boolean, array, or object.',
        },
      },
    },
    returns: {
      type: 'object',
      description: 'The result of the node execution, could be anything',
    },
    scope: {
      urlPatterns: ['<all_urls>'],
      agents: ['workflow'],
    },
  },
];

export const ALL_TOOLS = [
  ...TAB_TOOLKIT_TOOLS,
  ...WEB_TOOLKIT_ACTION_TOOLS,
  ...WEB_TOOLKIT_ANALYZE_TOOLS,
  ...DEV_TOOLKIT_TOOLS,
  ...WORKFLOW_TOOLKIT_TOOLS,
];
