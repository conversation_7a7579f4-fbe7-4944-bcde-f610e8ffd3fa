import {
  Too<PERSON><PERSON><PERSON>,
  Too<PERSON><PERSON>xecutor,
  ToolCallResult,
  ToolDescription,
  AgentRuntimeConfig,
} from '@the-agent/shared';
import {
  executeToolInBackground,
  getWebContext,
  isToolScopeMatch,
  parseToolParams,
} from '~/utils/toolkit';
import { ALL_TOOLS } from './tool-descriptions';
import { db } from '~/storages/indexdb';
import { WorkflowNodeType } from '~/background/workflow/types';

abstract class DefaultToolExecutor implements ToolExecutor {
  abstract getTools(agentId: string): Promise<ToolDescription[]>;

  abstract executeInternal(
    name: string,
    params: unknown,
    config: AgentRuntimeConfig
  ): Promise<ToolCallResult>;

  async execute(toolCall: ToolCall, options: AgentRuntimeConfig): Promise<ToolCallResult> {
    try {
      if (!toolCall.function.name) {
        throw new Error('Tool name is required');
      }

      const toolName = toolCall.function.name;
      const params = parseToolParams(toolCall);
      if (toolName.startsWith('TabToolkit_') || toolName.startsWith('WebToolkit_')) {
        return await executeToolInBackground(toolName, params);
      } else {
        return this.executeInternal(toolName, params, options);
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : String(error);
      console.error('Tool execution failed:', message);
      return { success: false, error: message };
    }
  }
}

export class DevToolExecutor extends DefaultToolExecutor {
  async executeInternal(
    name: string,
    params: unknown,
    config: AgentRuntimeConfig
  ): Promise<ToolCallResult> {
    if (name.startsWith('DevToolkit_')) {
      return handleDevToolkit(name, params, config.chatOptions!.conversationId);
    } else {
      throw new Error(`Unsupported tool: ${name}`);
    }
  }

  async getTools(agentId: string): Promise<ToolDescription[]> {
    return ALL_TOOLS.filter(tool => isToolScopeMatch(tool, agentId, null));
  }
}

export class WorkflowToolExecutor extends DefaultToolExecutor {
  async executeInternal(
    name: string,
    params: unknown,
    config: AgentRuntimeConfig
  ): Promise<ToolCallResult> {
    if (name.startsWith('DevToolkit_')) {
      return handleDevToolkit(name, params, config.chatOptions.conversationId);
    } else if (name.startsWith('WorkflowToolkit_')) {
      return handleWorkflowToolkit(name, params, config.chatOptions.conversationId);
    } else {
      throw new Error(`Unsupported tool: ${name}`);
    }
  }

  async getTools(agentId: string): Promise<ToolDescription[]> {
    return ALL_TOOLS.filter(tool => isToolScopeMatch(tool, agentId, null));
  }
}

export class BrowserToolExecutor extends DefaultToolExecutor {
  async executeInternal(name: string): Promise<ToolCallResult> {
    throw new Error(`Unsupported tool: ${name}`);
  }

  async getTools(agentId: string): Promise<ToolDescription[]> {
    const webctx = await getWebContext();
    return ALL_TOOLS.filter(tool => isToolScopeMatch(tool, agentId, webctx));
  }

  isFinalToolCall(toolCall: ToolCall): boolean {
    return toolCall.function.name === 'DevToolkit_deliver';
  }
}

async function handleDevToolkit(
  name: string,
  params: unknown,
  convId: number
): Promise<ToolCallResult> {
  if (name !== 'DevToolkit_deliver') {
    if (!convId) {
      return { success: false, error: 'Conversation ID is required' };
    }
    const miniappId = await db.getMiniappByConversationId(convId);
    if (!miniappId) {
      return { success: false, error: 'Miniapp not found' };
    }
    const { script } = params as { script: string };
    await db.updateMiniapp(miniappId.id, {
      developing: {
        code: JSON.stringify(script),
        version: 1,
        updated_at: Date.now(),
      },
    });
    return { success: true };
  } else {
    throw new Error(`Unsupported tool: ${name}`);
  }
}

async function handleWorkflowToolkit(
  name: string,
  params: unknown,
  convId: number
): Promise<ToolCallResult> {
  if (name === 'WorkflowToolkit_run') {
    if (!convId) {
      return { success: false, error: 'Conversation ID is required' };
    }
    const miniappId = await db.getMiniappByConversationId(convId);
    if (!miniappId) {
      return { success: false, error: 'Miniapp not found' };
    }
    const { parameters = {} } = params as { parameters?: Record<string, unknown> };
    const code = miniappId.developing?.code;
    if (!code) {
      return { success: false, error: 'Developing code not found' };
    }
    const { workflow } = JSON.parse(code) as { workflow: WorkflowNodeType[] };
    if (!workflow) {
      return { success: false, error: 'Workflow not found' };
    }
    return await chrome.runtime.sendMessage({
      name: 'workflow:execute',
      body: {
        workflow,
        parameters,
      },
    });
  } else if (name === 'WorkflowToolkit_runNode') {
    const { node, parameters = {} } = params as {
      node: WorkflowNodeType;
      parameters?: Record<string, unknown>;
    };
    return await chrome.runtime.sendMessage({
      name: 'workflow:executeNode',
      body: {
        node,
        parameters,
      },
    });
  } else if (name === 'WorkflowToolkit_deliver') {
    const { workflow, parameters = [] } = params as {
      workflow: WorkflowNodeType[];
      parameters?: Record<string, unknown>;
    };
    if (!workflow) {
      return { success: false, error: 'Workflow is required' };
    }
    const miniapp = await db.getMiniappByConversationId(convId);
    if (!miniapp) {
      return { success: false, error: 'Miniapp not found' };
    }
    await db.updateMiniapp(miniapp.id, {
      developing: {
        code: JSON.stringify({
          workflow,
          parameters,
        }),
        version: miniapp.developing?.version || 1,
        updated_at: Date.now(),
      },
    });
    return { success: true };
  } else {
    throw new Error(`Unsupported tool: ${name}`);
  }
}
