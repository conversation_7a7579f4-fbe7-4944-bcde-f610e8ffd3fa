/**
 * Workflow Node Type Definitions
 *
 * This file defines all the node types for the config-based workflow system.
 * Each node represents a step in a workflow that can be executed across multiple pages.
 */

// Runtime input types - allow referencing outputs from previous nodes
export type RuntimeInput<T> = T | string; // Either static value or variable reference like "{{nodeId.result}}"

// Base node interface - common properties for all node types
interface BaseNode {
  id: string; // Unique identifier and human-readable name
  description?: string;
  timeout?: number; // ms, default 30000
}

// 1. SCRIPT - Execute JavaScript in page context
export interface ScriptNode extends BaseNode {
  type: 'script';
  target: {
    tabId?: RuntimeInput<number>;
  };
  code: RuntimeInput<string>; // JavaScript to execute
}

// 2. NAVIGATION - Navigate to URL or manage tabs
export interface NavigationNode extends BaseNode {
  type: 'navigation';
  action: 'open' | 'close' | 'switch' | 'reload' | 'back' | 'forward';
  target?: {
    url?: RuntimeInput<string>; // Required for 'open'
    tabId?: RuntimeInput<number>; // Required for 'close', 'switch', optional for others
  };
  loadTimeout?: number; // Timeout for waiting (default: 30000ms)
}

// 3. WAIT - Simple time delay
export interface WaitNode extends BaseNode {
  type: 'wait';
  duration: RuntimeInput<number>; // Milliseconds to wait
}

// 4. DEBUGGER - Chrome DevTools Protocol commands
export interface DebuggerNode extends BaseNode {
  type: 'debugger';
  action: 'attach' | 'detach' | 'command';
  target: {
    tabId?: RuntimeInput<number>;
  };
  command?: RuntimeInput<string>; // CDP command like 'Input.dispatchMouseEvent'
  params?: Record<string, RuntimeInput<any>>; // CDP command parameters
}

// 5. WORKFLOW - Sequence execution (with optional iteration)
export interface WorkflowNode extends BaseNode {
  type: 'workflow';
  nodes: WorkflowNodeType[]; // First node is the entry point

  // Iteration parameters (optional - makes it work like foreach)
  repeat?: RuntimeInput<number>; // Run the nested nodes x times
  iterable?: RuntimeInput<string[]>; // Array to iterate over
}

// Union type for all node types
export type WorkflowNodeType = ScriptNode | NavigationNode | WaitNode | DebuggerNode | WorkflowNode;

// Resolved node types (after runtime input resolution)
export interface ResolvedScriptNode extends Omit<ScriptNode, 'target' | 'code'> {
  target: {
    tabId?: number;
  };
  code: string;
}

export interface ResolvedNavigationNode extends Omit<NavigationNode, 'target'> {
  target?: {
    url?: string;
    tabId?: number;
  };
  loadTimeout?: number;
}

export interface ResolvedWaitNode extends Omit<WaitNode, 'duration'> {
  duration: number;
}

export interface ResolvedDebuggerNode extends Omit<DebuggerNode, 'target' | 'command' | 'params'> {
  target: {
    tabId?: number;
  };
  command?: string;
  params?: Record<string, any>;
}

export interface ResolvedWorkflowNode extends Omit<WorkflowNode, 'repeat' | 'iterable'> {
  repeat?: number;
  iterable?: any[];
  nodes: WorkflowNodeType[]; // Keep as unresolved for recursive resolution
}

// Execution context and results
export interface WorkflowExecutionContext {
  workflowId: string;
  variables: Record<string, string>; // Global variables as JSON strings
  currentNodeId?: string;
  startTime: number;
  tabContexts: Map<number, any>; // Tab-specific data
  settings: {
    defaultTimeout: number;
    maxRetries: number;
    debugMode: boolean;
  };
}

export interface WorkflowResult {
  success: boolean;
  result?: any;
  error?: string;
  executionTime?: number;
  nodeResults?: Record<string, any>; // Results from each node
}

export interface NodeExecutionResult {
  success: boolean;
  result?: string; // Always a JSON string
  error?: string;
}

// Runtime input resolution utilities
export interface RuntimeResolver {
  /**
   * Resolve a runtime input value using current workflow variables
   * @param input Static value or variable reference like "{{nodeId}}"
   * @param variables Current workflow variables (JSON strings)
   * @returns Resolved value
   */
  resolve<T>(input: RuntimeInput<T>, variables: Record<string, string>): T;

  /**
   * Resolve all runtime inputs in an object
   * @param obj Object containing runtime inputs
   * @param variables Current workflow variables (JSON strings)
   * @returns Object with all inputs resolved
   */
  resolveObject<T>(obj: T, variables: Record<string, string>): T;
}
