import {
  WorkflowNodeType,
  WorkflowExecutionContext,
  WorkflowResult,
  NodeExecutionResult,
  WorkflowNode,
  ResolvedScriptNode,
  ResolvedNavigationNode,
  ResolvedWaitNode,
  ResolvedWorkflowNode,
  ResolvedDebuggerNode,
} from './types';
import { runtimeResolver } from './runtime-resolver';
import { cdpExecutor } from './cdp-executor';
import { attachDebuggerIfNeeded, detachDebuggerIfAttached } from './debugger-utils';
import { userScriptManager } from './user-scripts';

/**
 * Main workflow runtime engine
 */
export class WorkflowRuntime {
  /**
   * Execute a single node with parameters
   */
  async executeNode(
    node: WorkflowNodeType,
    parameters: Record<string, unknown> = {}
  ): Promise<NodeExecutionResult> {
    const context: WorkflowExecutionContext = {
      workflowId: 'single-node-execution',
      variables: {},
      startTime: Date.now(),
      tabContexts: new Map(),
      settings: {
        defaultTimeout: 30000,
        maxRetries: 0,
        debugMode: false,
      },
    };

    // Add parameters to variables with __param__ prefix
    for (const [name, value] of Object.entries(parameters)) {
      context.variables[`__param__${name}`] = JSON.stringify(value);
    }

    return await this.executeNodeInternal(node, context);
  }

  /**
   * Execute a complete workflow
   */
  async executeWorkflow(
    workflow: WorkflowNode,
    parameters: Record<string, unknown> = {}
  ): Promise<WorkflowResult> {
    const context: WorkflowExecutionContext = {
      workflowId: workflow.id,
      variables: {},
      startTime: Date.now(),
      tabContexts: new Map(),
      settings: {
        defaultTimeout: 30000,
        maxRetries: 0,
        debugMode: false,
      },
    };

    // Add parameters to variables with __param__ prefix
    for (const [name, value] of Object.entries(parameters)) {
      context.variables[`__param__${name}`] = JSON.stringify(value);
    }

    try {
      // Execute all nodes sequentially
      for (let i = 0; i < workflow.nodes.length; i++) {
        const node = workflow.nodes[i];
        context.currentNodeId = node.id;

        const result = await this.executeNodeInternal(node, context);

        if (!result.success) {
          return {
            success: false,
            error: `Node '${node.id}' failed: ${result.error}`,
            executionTime: Date.now() - context.startTime,
          };
        }

        // Always store result in variables using node ID
        if (result.result) {
          context.variables[node.id] = result.result;
        }
      }

      // Return the result of the last node
      const lastNode = workflow.nodes[workflow.nodes.length - 1];
      const finalResult = lastNode
        ? context.variables[lastNode.id]
        : JSON.stringify({ success: true });

      return {
        success: true,
        result: finalResult ? JSON.parse(finalResult) : undefined,
        executionTime: Date.now() - context.startTime,
      };
    } catch (error) {
      const message = error instanceof Error ? error.message : String(error);
      return {
        success: false,
        error: message,
        executionTime: Date.now() - context.startTime,
      };
    } finally {
      // Cleanup: detach any attached debuggers and user scripts
      await this.cleanupDebuggers();
      await userScriptManager.cleanupAll();
    }
  }

  /**
   * Execute a single node
   */
  private async executeNodeInternal(
    node: WorkflowNodeType,
    context: WorkflowExecutionContext
  ): Promise<NodeExecutionResult> {
    try {
      // Resolve all runtime inputs in the node
      const resolvedNode = runtimeResolver.resolveObject(
        node,
        context.variables
      ) as WorkflowNodeType;

      switch (resolvedNode.type) {
        case 'script':
          return await this.executeScriptNode(resolvedNode as ResolvedScriptNode, context);
        case 'navigation':
          return await this.executeNavigationNode(resolvedNode as ResolvedNavigationNode, context);
        case 'wait':
          return await this.executeWaitNode(resolvedNode as ResolvedWaitNode, context);
        case 'debugger':
          return await this.executeDebuggerNode(resolvedNode as ResolvedDebuggerNode, context);
        case 'workflow':
          return await this.executeWorkflowNode(resolvedNode as ResolvedWorkflowNode, context);
        default:
          return {
            success: false,
            error: `Unknown node type: ${(node as WorkflowNodeType).type}`,
          };
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : String(error);
      return {
        success: false,
        error: `Failed to resolve node inputs: ${message}`,
      };
    }
  }

  /**
   * Execute a script node using user script registration
   */
  private async executeScriptNode(
    node: ResolvedScriptNode,
    _context: WorkflowExecutionContext
  ): Promise<NodeExecutionResult> {
    try {
      const tabId = node.target?.tabId || (await this.getCurrentTabId());
      // Use CDP Runtime.evaluate to avoid CSP and immediate execution on current page
      const result = await cdpExecutor.execute({
        tabId,
        code: node.code,
        timeoutMs: node.timeout || 30000,
      });
      return result;
    } catch (error) {
      const message = error instanceof Error ? error.message : String(error);
      return {
        success: false,
        error: message,
      };
    }
  }

  /**
   * Execute a navigation node
   */
  private async executeNavigationNode(
    node: ResolvedNavigationNode,
    _context: WorkflowExecutionContext
  ): Promise<NodeExecutionResult> {
    try {
      let result: unknown;
      const loadTimeout = node.loadTimeout || 30000;

      switch (node.action) {
        case 'open':
          if (!node.target?.url) {
            throw new Error('URL required for open action');
          }
          const tab = await chrome.tabs.create({
            url: node.target.url,
            active: true,
          });
          result = tab.id;

          // Always wait for page to load
          if (tab.id) {
            await this.waitForTabLoad(tab.id, loadTimeout);
          }
          break;

        case 'close':
          if (!node.target?.tabId) {
            throw new Error('Tab ID required for close action');
          }
          await chrome.tabs.remove(node.target.tabId);
          result = true;
          break;

        case 'switch':
          if (!node.target?.tabId) {
            throw new Error('Tab ID required for switch action');
          }
          await chrome.tabs.update(node.target.tabId, { active: true });
          result = node.target.tabId;
          break;

        case 'reload':
          const tabId = node.target?.tabId || (await this.getCurrentTabId());
          await chrome.tabs.reload(tabId);
          result = tabId;

          // Always wait for page to reload
          await this.waitForTabLoad(tabId, loadTimeout);
          break;

        case 'back':
          const backTabId = node.target?.tabId || (await this.getCurrentTabId());
          await chrome.tabs.goBack(backTabId);
          result = backTabId;

          // Always wait for navigation to complete
          await this.waitForTabLoad(backTabId, loadTimeout);
          break;

        case 'forward':
          const forwardTabId = node.target?.tabId || (await this.getCurrentTabId());
          await chrome.tabs.goForward(forwardTabId);
          result = forwardTabId;

          // Always wait for navigation to complete
          await this.waitForTabLoad(forwardTabId, loadTimeout);
          break;

        default:
          throw new Error(`Unknown navigation action: ${node.action}`);
      }

      return {
        success: true,
        result: JSON.stringify(result),
      };
    } catch (error) {
      const message = error instanceof Error ? error.message : String(error);
      return {
        success: false,
        error: message,
      };
    }
  }

  /**
   * Execute a wait node
   */
  private async executeWaitNode(
    node: ResolvedWaitNode,
    _context: WorkflowExecutionContext
  ): Promise<NodeExecutionResult> {
    try {
      await new Promise(resolve => setTimeout(resolve, node.duration));

      return {
        success: true,
        result: JSON.stringify(true),
      };
    } catch (error) {
      const message = error instanceof Error ? error.message : String(error);
      return {
        success: false,
        error: message,
      };
    }
  }

  /**
   * Execute a workflow node (supports both sub-workflows and iteration)
   */
  private async executeWorkflowNode(
    node: ResolvedWorkflowNode,
    context: WorkflowExecutionContext
  ): Promise<NodeExecutionResult> {
    try {
      // Check if this is an iteration (foreach-like behavior)
      if (node.repeat || node.iterable) {
        return await this.executeIterativeWorkflow(node, context);
      } else {
        return await this.executeSequentialWorkflow(node, context);
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : String(error);
      return {
        success: false,
        error: message,
      };
    }
  }

  /**
   * Execute workflow with iteration (formerly foreach logic)
   */
  private async executeIterativeWorkflow(
    node: ResolvedWorkflowNode,
    context: WorkflowExecutionContext
  ): Promise<NodeExecutionResult> {
    let items: unknown[];

    if (node.repeat) {
      // Create array of indices for repeat mode
      items = Array.from({ length: node.repeat }, (_, i) => i);
    } else if (node.iterable) {
      // Use provided array
      items = node.iterable;
    } else {
      throw new Error('Either repeat or iterable must be specified for iterative workflow');
    }

    const results: string[] = [];

    // Execute nested nodes for each item
    for (let i = 0; i < items.length; i++) {
      const item = items[i];

      // Set iteration context variables
      const iterationContext = {
        ...context,
        variables: {
          ...context.variables,
          __item__: JSON.stringify(item),
          __index__: JSON.stringify(i),
        },
      };

      // Execute all nested nodes for this iteration
      const iterationResults: unknown[] = [];
      for (const nestedNode of node.nodes) {
        const result = await this.executeNodeInternal(nestedNode, iterationContext);

        if (!result.success) {
          return {
            success: false,
            error: `Iteration ${i}, node '${nestedNode.id}' failed: ${result.error}`,
          };
        }

        if (result.result) {
          iterationResults.push(JSON.parse(result.result));
        }
      }

      // Collect results from this iteration
      results.push(
        JSON.stringify(iterationResults.length === 1 ? iterationResults[0] : iterationResults)
      );
    }

    return {
      success: true,
      result: JSON.stringify(results),
    };
  }

  /**
   * Execute workflow sequentially (sub-workflow behavior)
   */
  private async executeSequentialWorkflow(
    node: ResolvedWorkflowNode,
    context: WorkflowExecutionContext
  ): Promise<NodeExecutionResult> {
    const results: unknown[] = [];

    // Execute all nested nodes sequentially
    for (const nestedNode of node.nodes) {
      const result = await this.executeNodeInternal(nestedNode, context);

      if (!result.success) {
        return {
          success: false,
          error: `Sub-workflow node '${nestedNode.id}' failed: ${result.error}`,
        };
      }

      if (result.result) {
        results.push(JSON.parse(result.result));
      }
    }

    // Return result of last node, or all results if multiple
    const finalResult = results.length === 1 ? results[0] : results;
    return {
      success: true,
      result: JSON.stringify(finalResult),
    };
  }

  /**
   * Execute a debugger node
   */
  private async executeDebuggerNode(
    node: ResolvedDebuggerNode,
    _context: WorkflowExecutionContext
  ): Promise<NodeExecutionResult> {
    try {
      const tabId = node.target?.tabId || (await this.getCurrentTabId());

      switch (node.action) {
        case 'attach':
          await attachDebuggerIfNeeded(tabId, '1.0');
          return { success: true, result: JSON.stringify(true) };

        case 'detach':
          await detachDebuggerIfAttached(tabId);
          return { success: true, result: JSON.stringify(true) };

        case 'command':
          if (!node.command) {
            throw new Error('Command required for debugger command action');
          }

          // Ensure debugger is attached
          await attachDebuggerIfNeeded(tabId, '1.0');

          const result = await chrome.debugger.sendCommand(
            { tabId },
            node.command,
            node.params || {}
          );
          return { success: true, result: JSON.stringify(result) };

        default:
          throw new Error(`Unknown debugger action: ${node.action}`);
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : String(error);
      return {
        success: false,
        error: message,
      };
    }
  }

  // Helper methods

  private async getCurrentTabId(): Promise<number> {
    const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
    if (!tabs[0]?.id) {
      throw new Error('No active tab found');
    }
    return tabs[0].id;
  }

  private async waitForTabLoad(tabId: number, timeout: number): Promise<void> {
    return new Promise((resolve, reject) => {
      const timer = setTimeout(() => {
        chrome.tabs.onUpdated.removeListener(listener);
        reject(new Error(`Tab load timeout after ${timeout}ms`));
      }, timeout);

      const listener = (updatedTabId: number, changeInfo: chrome.tabs.TabChangeInfo) => {
        if (updatedTabId === tabId && changeInfo.status === 'complete') {
          clearTimeout(timer);
          chrome.tabs.onUpdated.removeListener(listener);
          resolve();
        }
      };

      chrome.tabs.onUpdated.addListener(listener);
    });
  }

  private async cleanupDebuggers(): Promise<void> {
    // best-effort: no-op, we now use stateless utils
  }
}

// Singleton instance
export const workflowRuntime = new WorkflowRuntime();
