import { attachDebuggerIfNeeded, detachDebuggerIfAttached } from './debugger-utils';

/**
 * CDP (Chrome DevTools Protocol) executor for running code via chrome.debugger
 * Uses Runtime.evaluate in the MAIN world and returns standardized results.
 */

export interface CdpExecuteOptions {
  tabId: number;
  code: string; // Arbitrary JS to run in the page
  timeoutMs?: number; // Optional timeout; default 30s
}

export interface CdpExecuteResult {
  success: boolean;
  result?: string; // JSON string (or undefined on error)
  error?: string;
}

export class CdpExecutor {
  async execute(options: CdpExecuteOptions): Promise<CdpExecuteResult> {
    const { tabId, code } = options;

    try {
      // Ensure debugger is attached using stateless util
      await attachDebuggerIfNeeded(tabId, '1.0');
      await chrome.debugger.sendCommand({ tabId }, 'Runtime.enable').catch(() => {});

      // Normalize user code for Runtime.evaluate compatibility:
      // Handles:
      // - IIFE function/arrow: return its value directly
      // - Plain anonymous/arrow function literal: invoke it and return value
      // - Multi-statement/definitions: call main() if present; otherwise return `result` if set
      const trimmed = code.trim();
      const noTrail = trimmed.replace(/;\s*$/, '');

      const isIifeFunction = /^\(\s*(async\s+)?function\b[\s\S]*\)\s*\(/.test(trimmed);
      const isIifeArrow = /^\(\s*(async\s+)?\(?[\s\S]*?\)?\s*=>[\s\S]*\)\s*\(/.test(trimmed);
      const isAnonFn = /^(async\s+)?function\s*\(/.test(trimmed);
      const isArrowFn = /^\s*(async\s+)?\(?[\w\s,]*\)?\s*=>/.test(trimmed);
      const anonImmediatelyInvoked = isAnonFn && /\}\s*\(\s*\)\s*;?$/.test(trimmed);

      let body: string;
      if (isIifeFunction || isIifeArrow || anonImmediatelyInvoked) {
        // Already an IIFE - just return its value
        body = `return ${noTrail};`;
      } else if (isAnonFn || isArrowFn) {
        // Function literal - define then call
        body = `const __fn__ = ${noTrail};\nreturn await __fn__();`;
      } else {
        // Declarations/statements - support main() or result variable
        body = `${code}\nif (typeof main === 'function') return await main();\nif (typeof result !== 'undefined') return result;\nreturn undefined;`;
      }

      const wrapped = `(() => Promise.resolve((async () => {\ntry {\n${body}\n} catch (e) { throw e }\n})()))()\n//# sourceURL=mysta://workflow-eval.js`;

      // Evaluate in page context; awaitPromise allows awaiting async results
      const evalResult = await chrome.debugger.sendCommand({ tabId }, 'Runtime.evaluate', {
        expression: wrapped,
        includeCommandLineAPI: false,
        awaitPromise: true,
        returnByValue: true,
      });

      // Check for exception
      // evalResult: { result?: { value?: any }, exceptionDetails?: any }
      if ((evalResult as any).exceptionDetails) {
        const details = (evalResult as any).exceptionDetails;
        const message =
          details?.exception?.description || details?.text || 'Runtime.evaluate exception';
        return { success: false, error: String(message) };
      }

      const value = (evalResult as any)?.result?.value;
      return { success: true, result: JSON.stringify(value) };
    } catch (error) {
      const msg = error instanceof Error ? error.message : String(error);
      return { success: false, error: msg };
    } finally {
      await detachDebuggerIfAttached(tabId);
    }
  }
}

export const cdpExecutor = new CdpExecutor();
